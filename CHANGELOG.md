# Change Log

All notable changes to the "zephyr-clock-configurator" extension will be documented in this file.

## [0.1.0] - 2024-01-XX

### Added
- Initial release of Zephyr Clock Configurator extension
- VSCode extension with activity bar integration
- Project tree view for Zephyr project detection
- React-based webview UI for clock configuration
- Python FastAPI backend for file parsing and data management
- Support for device tree (.dtsi, .overlay) file parsing
- Support for YAML and Kconfig file parsing
- Real-time file system monitoring
- SQLite database for configuration storage
- Clock source selection (HSI, HSE, PLL, etc.)
- Frequency configuration with unit selection (Hz, KHz, MHz)
- Project scanning and automatic detection
- Error handling and user feedback
- Comprehensive logging and debugging support

### Features
- **Project Management**
  - Automatic Zephyr project detection
  - Project tree view with file organization
  - Real-time project scanning
  - Support for multiple projects in workspace

- **Clock Configuration**
  - Interactive clock configuration interface
  - Multiple clock source options
  - Flexible frequency settings with unit conversion
  - Enable/disable clock configurations
  - Configuration validation and error checking

- **File Parsing**
  - Device tree source file parsing
  - YAML configuration file support
  - Kconfig file parsing
  - CMakeLists.txt and prj.conf detection
  - Automatic file change monitoring

- **Backend Services**
  - FastAPI REST API
  - SQLite database integration
  - File system watching
  - Project metadata management
  - Configuration persistence

- **User Interface**
  - VSCode-integrated activity bar
  - React-based configuration panel
  - Real-time status indicators
  - Error messaging and feedback
  - Loading states and progress indication

### Technical Details
- Built with TypeScript for VSCode extension
- React 18+ with TypeScript for UI
- Python 3.9+ with FastAPI for backend
- SQLAlchemy for database operations
- Watchdog for file system monitoring
- Vite for React build tooling
- ESLint and TypeScript for code quality

### Known Issues
- Device tree parsing is simplified and may not handle all DT syntax
- Limited to basic clock configuration options
- File watching may have performance impact on large projects
- Python backend requires manual installation of dependencies

### Future Enhancements
- Advanced device tree parsing
- More clock configuration options
- Integration with Zephyr build system
- Configuration templates and presets
- Multi-board support
- Export/import configurations
- Visual clock tree representation
