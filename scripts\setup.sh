#!/bin/bash

# Z<PERSON>hyr Clock Configurator Extension Setup Script
# This script sets up the development environment for the extension

set -e

echo "🚀 Setting up Zephyr Clock Configurator Extension..."

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.9+ and try again."
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1-2)
if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
    echo "❌ Python 3.9+ is required. Current version: $(python3 --version)"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Install VSCode extension dependencies
echo "📦 Installing VSCode extension dependencies..."
npm install

# Install React UI dependencies
echo "📦 Installing React UI dependencies..."
cd webview-ui
npm install
cd ..

# Install Python backend dependencies
echo "📦 Installing Python backend dependencies..."
cd python-backend
python3 -m pip install -r requirements.txt
cd ..

# Build the extension
echo "🔨 Building the extension..."
npm run compile

# Build React UI
echo "🔨 Building React UI..."
npm run build-webview

echo "✅ Setup completed successfully!"
echo ""
echo "🎯 Next steps:"
echo "1. Open VSCode in this directory: code ."
echo "2. Press F5 to launch the Extension Development Host"
echo "3. Open a Zephyr project in the new VSCode window"
echo "4. Look for the clock icon in the Activity Bar"
echo ""
echo "🐛 For debugging:"
echo "- Use 'npm run watch' to watch TypeScript changes"
echo "- Use 'npm run dev-webview' to develop React UI"
echo "- Use the Python Backend launch configuration in VSCode"
echo ""
echo "📚 See README.md for detailed usage instructions"
