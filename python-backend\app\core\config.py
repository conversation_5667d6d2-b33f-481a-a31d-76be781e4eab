"""
Application configuration settings
"""

import os
from pathlib import Path
from typing import Optional

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Server settings
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    DEBUG: bool = False
    
    # Database settings
    DATABASE_URL: str = "sqlite:///./zephyr_clock_config.db"
    DATABASE_ECHO: bool = False
    
    # File watching settings
    WATCH_EXTENSIONS: list = [".dtsi", ".overlay", ".yaml", ".yml", ".conf"]
    WATCH_PATTERNS: list = ["**/CMakeLists.txt", "**/prj.conf", "**/Kconfig*"]
    
    # Project scanning settings
    MAX_SCAN_DEPTH: int = 10
    SCAN_TIMEOUT: int = 30  # seconds
    
    # API settings
    API_PREFIX: str = "/api"
    CORS_ORIGINS: list = ["*"]
    
    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Cache settings
    CACHE_TTL: int = 300  # 5 minutes
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_project_root() -> Path:
    """Get the project root directory"""
    return Path(__file__).parent.parent.parent


def get_data_dir() -> Path:
    """Get the data directory for storing files"""
    data_dir = get_project_root() / "data"
    data_dir.mkdir(exist_ok=True)
    return data_dir


def get_logs_dir() -> Path:
    """Get the logs directory"""
    logs_dir = get_project_root() / "logs"
    logs_dir.mkdir(exist_ok=True)
    return logs_dir
