import * as assert from 'assert';
import * as vscode from 'vscode';
import { ZephyrProjectScanner } from '../../utils/projectScanner';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('Extension should be present', () => {
        assert.ok(vscode.extensions.getExtension('zephyr-clock-configurator'));
    });

    test('Extension should activate', async () => {
        const extension = vscode.extensions.getExtension('zephyr-clock-configurator');
        if (extension) {
            await extension.activate();
            assert.ok(extension.isActive);
        }
    });

    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        
        const expectedCommands = [
            'zephyr.refreshProjects',
            'zephyr.launchConfigurator',
            'zephyr.selectProject'
        ];

        for (const command of expectedCommands) {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        }
    });
});

suite('Project Scanner Test Suite', () => {
    let scanner: ZephyrProjectScanner;

    setup(() => {
        scanner = new ZephyrProjectScanner();
    });

    test('Scanner should initialize', () => {
        assert.ok(scanner);
    });

    test('Scanner should handle empty workspace', async () => {
        // Mock empty workspace
        const result = await scanner.scanWorkspace();
        assert.ok(result);
        assert.ok(Array.isArray(result.projects));
        assert.ok(Array.isArray(result.errors));
    });

    test('Scanner should detect Zephyr project indicators', async () => {
        // This would require setting up a mock file system
        // For now, just test that the method exists and returns expected structure
        const mockProjectPath = '/mock/project/path';
        
        try {
            const details = await scanner.getProjectDetails(mockProjectPath);
            // Should return null for non-existent path
            assert.strictEqual(details, null);
        } catch (error) {
            // Expected for non-existent path
            assert.ok(true);
        }
    });
});
