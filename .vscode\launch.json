{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "${workspaceFolder}/npm: compile"}, {"name": "Extension Tests", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--extensionTestsPath=${workspaceFolder}/out/test/suite/index"], "outFiles": ["${workspaceFolder}/out/test/**/*.js"], "preLaunchTask": "${workspaceFolder}/npm: compile"}, {"name": "Python Backend", "type": "python", "request": "launch", "program": "${workspaceFolder}/python-backend/main.py", "cwd": "${workspaceFolder}/python-backend", "env": {"ZEPHYR_BACKEND_HOST": "127.0.0.1", "ZEPHYR_BACKEND_PORT": "8000", "PYTHONPATH": "${workspaceFolder}/python-backend"}, "console": "integratedTerminal"}]}