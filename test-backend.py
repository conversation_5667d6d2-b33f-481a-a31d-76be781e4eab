#!/usr/bin/env python3
"""
Test script to verify the Python backend server functionality
"""

import requests
import time
import sys
import subprocess
import os
from pathlib import Path

def test_backend_server():
    """Test the backend server functionality"""
    
    print("🧪 Testing Zephyr Clock Configurator Backend Server")
    print("=" * 50)
    
    # Start the server
    print("🚀 Starting Python backend server...")
    backend_dir = Path(__file__).parent / "python-backend"
    
    try:
        # Start server process
        server_process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd=backend_dir,
            env={**os.environ, "ZEPHYR_BACKEND_PORT": "8000"},
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        time.sleep(3)
        
        # Test health endpoint
        print("🔍 Testing health endpoint...")
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ Health check passed!")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False
        
        # Test API endpoints
        print("🔍 Testing API endpoints...")
        
        # Test projects endpoint
        try:
            response = requests.get("http://127.0.0.1:8000/api/projects", timeout=5)
            print(f"✅ Projects endpoint: {response.status_code}")
        except Exception as e:
            print(f"⚠️  Projects endpoint error: {e}")
        
        # Test shutdown endpoint
        print("🛑 Testing graceful shutdown...")
        try:
            response = requests.post("http://127.0.0.1:8000/api/shutdown", timeout=2)
            print("✅ Shutdown signal sent successfully")
        except Exception as e:
            print(f"⚠️  Shutdown error (expected): {e}")
        
        # Wait for server to stop
        time.sleep(2)
        
        print("✅ Backend server test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Backend server test failed: {e}")
        return False
    
    finally:
        # Ensure server is stopped
        try:
            server_process.terminate()
            server_process.wait(timeout=5)
        except:
            try:
                server_process.kill()
            except:
                pass

def test_extension_files():
    """Test that all required extension files are present"""
    
    print("\n📁 Testing Extension Files")
    print("=" * 30)
    
    required_files = [
        "package.json",
        "out/extension.js",
        "webview-ui/dist/index.html",
        "webview-ui/dist/assets/index.js",
        "python-backend/main.py",
        "python-backend/requirements.txt",
        "zephyr-clock-configurator-0.1.0.vsix"
    ]
    
    all_present = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            all_present = False
    
    return all_present

if __name__ == "__main__":
    print("🎯 Zephyr Clock Configurator - Complete Test Suite")
    print("=" * 60)
    
    # Test extension files
    files_ok = test_extension_files()
    
    # Test backend server
    server_ok = test_backend_server()
    
    print("\n📊 Test Results Summary")
    print("=" * 25)
    print(f"Extension Files: {'✅ PASS' if files_ok else '❌ FAIL'}")
    print(f"Backend Server:  {'✅ PASS' if server_ok else '❌ FAIL'}")
    
    if files_ok and server_ok:
        print("\n🎉 All tests passed! Extension is ready for use.")
        print("\n📋 Next Steps:")
        print("1. Restart VSCode to load the extension")
        print("2. Open a workspace with Zephyr projects")
        print("3. Look for 'Zephyr Clock Config' in the Activity Bar")
        print("4. The Python server will start automatically when you use the extension")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        sys.exit(1)
