"""
Project Pydantic schemas
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class ProjectBase(BaseModel):
    name: str = Field(..., description="Project name")
    path: str = Field(..., description="Project path")
    board_config: Optional[str] = Field(None, description="Board configuration")


class ProjectCreate(ProjectBase):
    cmake_file: Optional[str] = None
    prj_conf_file: Optional[str] = None
    dtsi_files: Optional[List[str]] = []
    yaml_files: Optional[List[str]] = []
    kconfig_files: Optional[List[str]] = []
    metadata: Optional[Dict[str, Any]] = {}


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    board_config: Optional[str] = None
    cmake_file: Optional[str] = None
    prj_conf_file: Optional[str] = None
    dtsi_files: Optional[List[str]] = None
    yaml_files: Optional[List[str]] = None
    kconfig_files: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class ProjectResponse(ProjectBase):
    id: int
    cmake_file: Optional[str]
    prj_conf_file: Optional[str]
    dtsi_files: List[str]
    yaml_files: List[str]
    kconfig_files: List[str]
    metadata: Dict[str, Any]
    last_scanned: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ProjectScanRequest(BaseModel):
    project_path: str = Field(..., description="Path to the project to scan")
    force_rescan: bool = Field(False, description="Force rescan even if recently scanned")


class ProjectScanResponse(BaseModel):
    project: ProjectResponse
    clock_configs_found: int
    errors: List[str] = []
    warnings: List[str] = []
