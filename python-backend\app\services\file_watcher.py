"""
File system watcher service for monitoring Zephyr project changes
"""

import asyncio
import logging
from pathlib import Path
from typing import Set, Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent

from app.core.config import settings

logger = logging.getLogger(__name__)


class ZephyrFileHandler(FileSystemEventHandler):
    """Handler for Zephyr project file system events"""
    
    def __init__(self, project_manager):
        super().__init__()
        self.project_manager = project_manager
        self.watched_extensions = set(settings.WATCH_EXTENSIONS)
        self.watched_patterns = set(settings.WATCH_PATTERNS)
        
    def should_process_file(self, file_path: str) -> bool:
        """Check if file should be processed based on extension or pattern"""
        path = Path(file_path)
        
        # Check extension
        if path.suffix in self.watched_extensions:
            return True
            
        # Check patterns
        for pattern in self.watched_patterns:
            if path.match(pattern):
                return True
                
        return False
    
    def on_modified(self, event: FileSystemEvent):
        """Handle file modification events"""
        if not event.is_directory and self.should_process_file(event.src_path):
            logger.info(f"File modified: {event.src_path}")
            asyncio.create_task(self._handle_file_change(event.src_path))
    
    def on_created(self, event: FileSystemEvent):
        """Handle file creation events"""
        if not event.is_directory and self.should_process_file(event.src_path):
            logger.info(f"File created: {event.src_path}")
            asyncio.create_task(self._handle_file_change(event.src_path))
    
    def on_deleted(self, event: FileSystemEvent):
        """Handle file deletion events"""
        if not event.is_directory and self.should_process_file(event.src_path):
            logger.info(f"File deleted: {event.src_path}")
            asyncio.create_task(self._handle_file_change(event.src_path))
    
    async def _handle_file_change(self, file_path: str):
        """Handle file change by finding and updating related project"""
        try:
            # Find project that contains this file
            project_path = self._find_project_root(file_path)
            if project_path:
                # Get existing project
                project = await self.project_manager.get_project_by_path(project_path)
                if project:
                    logger.info(f"Rescanning project due to file change: {project_path}")
                    # Trigger project rescan
                    from app.schemas.project import ProjectScanRequest
                    scan_request = ProjectScanRequest(
                        project_path=project_path,
                        force_rescan=True
                    )
                    await self.project_manager.scan_project(scan_request)
        except Exception as e:
            logger.error(f"Error handling file change {file_path}: {e}")
    
    def _find_project_root(self, file_path: str) -> Optional[str]:
        """Find the root directory of the Zephyr project containing the file"""
        path = Path(file_path)
        current_dir = path.parent if path.is_file() else path
        
        # Walk up the directory tree looking for Zephyr project indicators
        while current_dir != current_dir.parent:
            # Check for CMakeLists.txt or prj.conf
            if (current_dir / "CMakeLists.txt").exists() or (current_dir / "prj.conf").exists():
                return str(current_dir)
            current_dir = current_dir.parent
        
        return None


class FileWatcherService:
    """Service for watching file system changes in Zephyr projects"""
    
    def __init__(self, project_manager):
        self.project_manager = project_manager
        self.observer: Optional[Observer] = None
        self.watched_paths: Set[str] = set()
        self.handler = ZephyrFileHandler(project_manager)
    
    async def start(self):
        """Start the file watcher service"""
        try:
            self.observer = Observer()
            logger.info("File watcher service started")
        except Exception as e:
            logger.error(f"Failed to start file watcher service: {e}")
            raise
    
    async def stop(self):
        """Stop the file watcher service"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.observer = None
            logger.info("File watcher service stopped")
    
    async def watch_path(self, path: str):
        """Add a path to be watched"""
        if not self.observer:
            logger.warning("File watcher not started")
            return
        
        if path in self.watched_paths:
            return
        
        try:
            path_obj = Path(path)
            if path_obj.exists():
                self.observer.schedule(self.handler, str(path_obj), recursive=True)
                self.watched_paths.add(path)
                logger.info(f"Now watching path: {path}")
            else:
                logger.warning(f"Path does not exist: {path}")
        except Exception as e:
            logger.error(f"Failed to watch path {path}: {e}")
    
    async def unwatch_path(self, path: str):
        """Remove a path from being watched"""
        if path in self.watched_paths:
            # Note: watchdog doesn't provide a direct way to unwatch specific paths
            # In a production implementation, you might need to restart the observer
            # or maintain a mapping of watch handles
            self.watched_paths.discard(path)
            logger.info(f"Stopped watching path: {path}")
    
    async def watch_project(self, project_path: str):
        """Start watching a specific project"""
        await self.watch_path(project_path)
    
    async def unwatch_project(self, project_path: str):
        """Stop watching a specific project"""
        await self.unwatch_path(project_path)
    
    def is_watching(self, path: str) -> bool:
        """Check if a path is being watched"""
        return path in self.watched_paths
