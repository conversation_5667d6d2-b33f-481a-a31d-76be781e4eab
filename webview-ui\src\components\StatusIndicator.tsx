import React from 'react';

interface StatusIndicatorProps {
  isConnected: boolean;
  serverUrl: string;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  isConnected,
  serverUrl
}) => {
  return (
    <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
      <div className="status-dot"></div>
      <span>
        Backend Server: {isConnected ? 'Connected' : 'Disconnected'}
        {serverUrl && ` (${serverUrl})`}
      </span>
    </div>
  );
};
