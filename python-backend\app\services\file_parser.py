"""
Zephyr file parsing service for device tree and configuration files
"""

import os
import re
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ClockConfigData:
    """Data class for parsed clock configuration"""
    name: str
    source: str
    frequency: int
    unit: str
    enabled: bool
    device_tree_path: Optional[str] = None
    kconfig_option: Optional[str] = None
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ParseResult:
    """Result of file parsing operation"""
    clock_configs: List[ClockConfigData]
    device_tree_data: Dict[str, Any]
    kconfig_data: Dict[str, Any]
    errors: List[str]
    warnings: List[str]


class ZephyrFileParser:
    """Parser for Zephyr project files"""

    def __init__(self):
        self.clock_patterns = {
            'clock_frequency': re.compile(r'clock-frequency\s*=\s*<(\d+)>', re.IGNORECASE),
            'clock_source': re.compile(r'clocks\s*=\s*<&([^>]+)>', re.IGNORECASE),
            'clock_names': re.compile(r'clock-names\s*=\s*"([^"]+)"', re.IGNORECASE),
            'status': re.compile(r'status\s*=\s*"([^"]+)"', re.IGNORECASE),
        }
        
        self.kconfig_patterns = {
            'clock_config': re.compile(r'CONFIG_([A-Z_]*CLOCK[A-Z_]*)\s*=\s*([yn]|[0-9]+)', re.IGNORECASE),
            'frequency_config': re.compile(r'CONFIG_([A-Z_]*FREQ[A-Z_]*)\s*=\s*([0-9]+)', re.IGNORECASE),
        }

    def parse_project(self, project_path: str) -> ParseResult:
        """Parse all relevant files in a Zephyr project"""
        project_path = Path(project_path)
        result = ParseResult(
            clock_configs=[],
            device_tree_data={},
            kconfig_data={},
            errors=[],
            warnings=[]
        )

        try:
            # Parse device tree files
            dtsi_files = list(project_path.rglob("*.dtsi")) + list(project_path.rglob("*.overlay"))
            for dtsi_file in dtsi_files:
                try:
                    dt_result = self.parse_device_tree_file(dtsi_file)
                    result.device_tree_data[str(dtsi_file)] = dt_result
                    result.clock_configs.extend(dt_result.get('clock_configs', []))
                except Exception as e:
                    result.errors.append(f"Error parsing {dtsi_file}: {str(e)}")

            # Parse configuration files
            config_files = list(project_path.rglob("prj.conf")) + list(project_path.rglob("*.conf"))
            for config_file in config_files:
                try:
                    kconfig_result = self.parse_kconfig_file(config_file)
                    result.kconfig_data[str(config_file)] = kconfig_result
                    result.clock_configs.extend(kconfig_result.get('clock_configs', []))
                except Exception as e:
                    result.errors.append(f"Error parsing {config_file}: {str(e)}")

            # Parse YAML files
            yaml_files = list(project_path.rglob("*.yaml")) + list(project_path.rglob("*.yml"))
            for yaml_file in yaml_files:
                try:
                    yaml_result = self.parse_yaml_file(yaml_file)
                    if yaml_result:
                        result.device_tree_data[str(yaml_file)] = yaml_result
                except Exception as e:
                    result.warnings.append(f"Warning parsing {yaml_file}: {str(e)}")

        except Exception as e:
            result.errors.append(f"Error scanning project {project_path}: {str(e)}")

        return result

    def parse_device_tree_file(self, file_path: Path) -> Dict[str, Any]:
        """Parse a device tree source file"""
        result = {
            'file_path': str(file_path),
            'clock_configs': [],
            'nodes': {},
            'includes': []
        }

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract includes
            include_pattern = re.compile(r'#include\s*[<"]([^>"]+)[>"]')
            includes = include_pattern.findall(content)
            result['includes'] = includes

            # Parse device tree nodes
            nodes = self._parse_dt_nodes(content)
            result['nodes'] = nodes

            # Extract clock configurations
            clock_configs = self._extract_clock_configs_from_dt(nodes, str(file_path))
            result['clock_configs'] = clock_configs

        except Exception as e:
            logger.error(f"Error parsing device tree file {file_path}: {e}")
            raise

        return result

    def parse_kconfig_file(self, file_path: Path) -> Dict[str, Any]:
        """Parse a Kconfig configuration file"""
        result = {
            'file_path': str(file_path),
            'clock_configs': [],
            'configurations': {}
        }

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse configuration options
            for line in content.split('\n'):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                # Match clock-related configurations
                for pattern_name, pattern in self.kconfig_patterns.items():
                    match = pattern.match(line)
                    if match:
                        config_name = match.group(1)
                        config_value = match.group(2)
                        
                        result['configurations'][config_name] = config_value
                        
                        # Create clock config if it's a frequency setting
                        if 'FREQ' in config_name:
                            clock_config = ClockConfigData(
                                name=config_name,
                                source='kconfig',
                                frequency=int(config_value),
                                unit='Hz',
                                enabled=True,
                                kconfig_option=config_name,
                                description=f"Kconfig frequency setting: {config_name}"
                            )
                            result['clock_configs'].append(clock_config)

        except Exception as e:
            logger.error(f"Error parsing Kconfig file {file_path}: {e}")
            raise

        return result

    def parse_yaml_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Parse a YAML configuration file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if not isinstance(data, dict):
                return None

            # Look for clock-related configurations in YAML
            result = {
                'file_path': str(file_path),
                'data': data,
                'clock_configs': []
            }

            # Extract clock configurations from YAML structure
            self._extract_clock_configs_from_yaml(data, result['clock_configs'], str(file_path))

            return result

        except Exception as e:
            logger.warning(f"Could not parse YAML file {file_path}: {e}")
            return None

    def _parse_dt_nodes(self, content: str) -> Dict[str, Any]:
        """Parse device tree nodes from content"""
        nodes = {}
        
        # Simple regex-based parsing for device tree nodes
        # This is a simplified parser - a full parser would need proper DT grammar
        node_pattern = re.compile(r'(\w+):\s*(\w+)@([0-9a-fA-F]+)\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}', re.MULTILINE | re.DOTALL)
        
        for match in node_pattern.finditer(content):
            label = match.group(1)
            node_type = match.group(2)
            address = match.group(3)
            properties = match.group(4)
            
            node_data = {
                'label': label,
                'type': node_type,
                'address': address,
                'properties': self._parse_dt_properties(properties)
            }
            
            nodes[label] = node_data

        return nodes

    def _parse_dt_properties(self, properties_text: str) -> Dict[str, Any]:
        """Parse device tree properties"""
        properties = {}
        
        for pattern_name, pattern in self.clock_patterns.items():
            matches = pattern.findall(properties_text)
            if matches:
                properties[pattern_name] = matches

        return properties

    def _extract_clock_configs_from_dt(self, nodes: Dict[str, Any], file_path: str) -> List[ClockConfigData]:
        """Extract clock configurations from parsed device tree nodes"""
        clock_configs = []

        for node_name, node_data in nodes.items():
            properties = node_data.get('properties', {})
            
            # Check if this node has clock-related properties
            if any(key in properties for key in ['clock_frequency', 'clock_source', 'clock_names']):
                frequency_matches = properties.get('clock_frequency', [])
                source_matches = properties.get('clock_source', [])
                name_matches = properties.get('clock_names', [])
                status_matches = properties.get('status', [])
                
                # Determine if clock is enabled
                enabled = True
                if status_matches:
                    enabled = status_matches[0].lower() in ['okay', 'ok']
                
                # Create clock config
                if frequency_matches:
                    frequency = int(frequency_matches[0])
                    source = source_matches[0] if source_matches else 'unknown'
                    name = name_matches[0] if name_matches else node_name
                    
                    # Determine appropriate unit
                    freq_value, unit = self._determine_frequency_unit(frequency)
                    
                    clock_config = ClockConfigData(
                        name=name,
                        source=source,
                        frequency=freq_value,
                        unit=unit,
                        enabled=enabled,
                        device_tree_path=f"{file_path}:{node_name}",
                        description=f"Device tree clock configuration for {node_name}"
                    )
                    
                    clock_configs.append(clock_config)

        return clock_configs

    def _extract_clock_configs_from_yaml(self, data: Dict[str, Any], clock_configs: List[ClockConfigData], file_path: str):
        """Extract clock configurations from YAML data"""
        # Recursively search for clock-related configurations in YAML
        def search_yaml(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # Look for clock-related keys
                    if 'clock' in key.lower() or 'freq' in key.lower():
                        if isinstance(value, (int, float)):
                            freq_value, unit = self._determine_frequency_unit(int(value))
                            
                            clock_config = ClockConfigData(
                                name=key,
                                source='yaml',
                                frequency=freq_value,
                                unit=unit,
                                enabled=True,
                                description=f"YAML clock configuration: {current_path}",
                                metadata={'yaml_path': current_path}
                            )
                            clock_configs.append(clock_config)
                    
                    search_yaml(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    search_yaml(item, f"{path}[{i}]")

        search_yaml(data)

    def _determine_frequency_unit(self, frequency_hz: int) -> Tuple[int, str]:
        """Determine the most appropriate unit for a frequency value"""
        if frequency_hz >= 1000000 and frequency_hz % 1000000 == 0:
            return frequency_hz // 1000000, "MHz"
        elif frequency_hz >= 1000 and frequency_hz % 1000 == 0:
            return frequency_hz // 1000, "KHz"
        else:
            return frequency_hz, "Hz"
