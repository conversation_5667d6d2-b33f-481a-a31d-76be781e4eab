"""
Tests for the API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import tempfile
import os

from app.core.database import Base, get_db
from main import app


# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create tables
Base.metadata.create_all(bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


class TestAPI:
    """Test cases for API endpoints"""

    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()

    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
        # Clean up test database
        if os.path.exists("test.db"):
            os.remove("test.db")

    def test_health_check(self):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data

    def test_get_projects_empty(self):
        """Test getting projects when none exist"""
        response = client.get("/api/projects")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0

    def test_scan_project_invalid_path(self):
        """Test scanning a project with invalid path"""
        scan_data = {
            "project_path": "/nonexistent/path",
            "force_rescan": False
        }
        
        response = client.post("/api/projects/scan", json=scan_data)
        assert response.status_code == 400

    def test_scan_project_valid_path(self):
        """Test scanning a valid project"""
        # Create a mock Zephyr project
        project_path = self.temp_dir
        
        # Create CMakeLists.txt
        cmake_file = os.path.join(project_path, "CMakeLists.txt")
        with open(cmake_file, 'w') as f:
            f.write("find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})")
        
        # Create prj.conf
        prj_conf_file = os.path.join(project_path, "prj.conf")
        with open(prj_conf_file, 'w') as f:
            f.write("CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=8000000")
        
        scan_data = {
            "project_path": project_path,
            "force_rescan": False
        }
        
        response = client.post("/api/projects/scan", json=scan_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "project" in data
        assert "clock_configs_found" in data
        assert "errors" in data
        assert "warnings" in data

    def test_get_project_not_found(self):
        """Test getting a project that doesn't exist"""
        response = client.get("/api/projects/999")
        assert response.status_code == 404

    def test_get_clock_configs_not_found(self):
        """Test getting clock configs for non-existent project"""
        response = client.get("/api/projects/nonexistent/clock-config")
        assert response.status_code == 404

    def test_update_clock_config_not_found(self):
        """Test updating clock config for non-existent project"""
        config_data = {
            "enabled": True,
            "source": "HSE",
            "frequency": 8,
            "unit": "MHz"
        }
        
        response = client.post("/api/projects/nonexistent/clock-config", json=config_data)
        assert response.status_code == 404


if __name__ == "__main__":
    pytest.main([__file__])
