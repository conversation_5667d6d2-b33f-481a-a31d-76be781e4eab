import React from 'react';
import { ZephyrProject } from '../types';

interface ProjectInfoProps {
  project: ZephyrProject;
  onScanProject: () => void;
  isScanning?: boolean;
}

export const ProjectInfo: React.FC<ProjectInfoProps> = ({
  project,
  onScanProject,
  isScanning = false
}) => {
  return (
    <div className="project-info">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2>Project Information</h2>
        <button
          className="btn btn-secondary"
          onClick={onScanProject}
          disabled={isScanning}
        >
          {isScanning ? 'Scanning...' : 'Rescan Project'}
        </button>
      </div>
      
      <div className="project-details">
        <p><strong>Name:</strong> {project.name}</p>
        <p><strong>Path:</strong> {project.path}</p>
        <p><strong>Board:</strong> {project.boardConfig}</p>
        
        {project.cmakeFile && (
          <p><strong>CMake File:</strong> {project.cmakeFile}</p>
        )}
        
        {project.prjConfFile && (
          <p><strong>Project Config:</strong> {project.prjConfFile}</p>
        )}
        
        <div className="file-counts">
          <p><strong>Device Tree Files:</strong> {project.dtsiFiles.length}</p>
          <p><strong>YAML Files:</strong> {project.yamlFiles.length}</p>
          <p><strong>Kconfig Files:</strong> {project.kconfigFiles.length}</p>
        </div>
        
        {project.dtsiFiles.length > 0 && (
          <details>
            <summary>Device Tree Files ({project.dtsiFiles.length})</summary>
            <ul>
              {project.dtsiFiles.map((file, index) => (
                <li key={index} title={file}>
                  {file.split('/').pop() || file}
                </li>
              ))}
            </ul>
          </details>
        )}
        
        {project.yamlFiles.length > 0 && (
          <details>
            <summary>YAML Files ({project.yamlFiles.length})</summary>
            <ul>
              {project.yamlFiles.map((file, index) => (
                <li key={index} title={file}>
                  {file.split('/').pop() || file}
                </li>
              ))}
            </ul>
          </details>
        )}
        
        {project.kconfigFiles.length > 0 && (
          <details>
            <summary>Kconfig Files ({project.kconfigFiles.length})</summary>
            <ul>
              {project.kconfigFiles.map((file, index) => (
                <li key={index} title={file}>
                  {file.split('/').pop() || file}
                </li>
              ))}
            </ul>
          </details>
        )}
      </div>
    </div>
  );
};
