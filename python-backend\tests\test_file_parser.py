"""
Tests for the Zephyr file parser
"""

import pytest
import tempfile
import os
from pathlib import Path

from app.services.file_parser import ZephyrFileParser, ClockConfigData


class TestZephyrFileParser:
    """Test cases for ZephyrFileParser"""

    def setup_method(self):
        """Set up test fixtures"""
        self.parser = ZephyrFileParser()
        self.temp_dir = tempfile.mkdtemp()

    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_test_file(self, filename: str, content: str) -> str:
        """Create a test file with given content"""
        file_path = os.path.join(self.temp_dir, filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w') as f:
            f.write(content)
        return file_path

    def test_parser_initialization(self):
        """Test parser initializes correctly"""
        assert self.parser is not None
        assert hasattr(self.parser, 'clock_patterns')
        assert hasattr(self.parser, 'kconfig_patterns')

    def test_parse_device_tree_file(self):
        """Test parsing device tree files"""
        dtsi_content = """
        / {
            clocks {
                hse_clock: hse_clock {
                    compatible = "fixed-clock";
                    clock-frequency = <8000000>;
                    status = "okay";
                };
            };
        };
        """
        
        file_path = self.create_test_file("test.dtsi", dtsi_content)
        result = self.parser.parse_device_tree_file(Path(file_path))
        
        assert result is not None
        assert 'file_path' in result
        assert 'clock_configs' in result
        assert 'nodes' in result

    def test_parse_kconfig_file(self):
        """Test parsing Kconfig files"""
        kconfig_content = """
        # Clock configuration
        CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=8000000
        CONFIG_CLOCK_CONTROL=y
        CONFIG_CLOCK_CONTROL_STM32_CUBE=y
        """
        
        file_path = self.create_test_file("prj.conf", kconfig_content)
        result = self.parser.parse_kconfig_file(Path(file_path))
        
        assert result is not None
        assert 'file_path' in result
        assert 'clock_configs' in result
        assert 'configurations' in result

    def test_parse_yaml_file(self):
        """Test parsing YAML files"""
        yaml_content = """
        clock:
          frequency: 8000000
          source: "HSE"
        system:
          clock_freq: 168000000
        """
        
        file_path = self.create_test_file("config.yaml", yaml_content)
        result = self.parser.parse_yaml_file(Path(file_path))
        
        assert result is not None
        assert 'file_path' in result
        assert 'data' in result

    def test_parse_project(self):
        """Test parsing a complete project"""
        # Create a mock Zephyr project structure
        self.create_test_file("CMakeLists.txt", "find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})")
        self.create_test_file("prj.conf", "CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=8000000")
        
        dtsi_content = """
        clk_hse: clk-hse {
            clock-frequency = <8000000>;
            status = "okay";
        };
        """
        self.create_test_file("boards/board.dtsi", dtsi_content)
        
        result = self.parser.parse_project(self.temp_dir)
        
        assert result is not None
        assert hasattr(result, 'clock_configs')
        assert hasattr(result, 'device_tree_data')
        assert hasattr(result, 'kconfig_data')
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')

    def test_determine_frequency_unit(self):
        """Test frequency unit determination"""
        # Test Hz
        freq, unit = self.parser._determine_frequency_unit(1000)
        assert freq == 1000
        assert unit == "Hz"
        
        # Test KHz
        freq, unit = self.parser._determine_frequency_unit(8000)
        assert freq == 8
        assert unit == "KHz"
        
        # Test MHz
        freq, unit = self.parser._determine_frequency_unit(168000000)
        assert freq == 168
        assert unit == "MHz"

    def test_clock_config_data(self):
        """Test ClockConfigData creation"""
        config = ClockConfigData(
            name="test_clock",
            source="HSE",
            frequency=8,
            unit="MHz",
            enabled=True,
            description="Test clock configuration"
        )
        
        assert config.name == "test_clock"
        assert config.source == "HSE"
        assert config.frequency == 8
        assert config.unit == "MHz"
        assert config.enabled is True
        assert config.description == "Test clock configuration"


if __name__ == "__main__":
    pytest.main([__file__])
