export interface ZephyrProject {
  name: string;
  path: string;
  boardConfig: string;
  dtsiFiles: string[];
  yamlFiles: string[];
  cmakeFile?: string;
  prjConfFile?: string;
  kconfigFiles: string[];
}

export interface ClockConfig {
  id?: number;
  projectId: string;
  configName: string;
  enabled: boolean;
  source: string;
  frequency: number;
  unit: 'Hz' | 'KHz' | 'MHz';
  description?: string;
}

export interface ClockConfigState {
  selectedProject: ZephyrProject | null;
  clockEnabled: boolean;
  clockSource: string;
  frequency: number;
  unit: 'Hz' | 'KHz' | 'MHz';
  isLoading: boolean;
  error: string | null;
  availableSources: string[];
  serverStatus: {
    isRunning: boolean;
    serverUrl: string;
  };
}

export interface WebviewMessage {
  type: string;
  payload?: any;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface VSCodeAPI {
  postMessage(message: WebviewMessage): void;
  getState(): any;
  setState(state: any): void;
}

declare global {
  interface Window {
    acquireVsCodeApi(): VSCodeAPI;
  }
}
