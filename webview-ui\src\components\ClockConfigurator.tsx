import React from 'react';
import { ClockConfigState } from '../types';

interface ClockConfiguratorProps {
  config: ClockConfigState;
  onChange: (config: Partial<ClockConfigState>) => void;
  onSave: () => void;
  disabled?: boolean;
}

export const ClockConfigurator: React.FC<ClockConfiguratorProps> = ({
  config,
  onChange,
  onSave,
  disabled = false
}) => {
  const clockSources = [
    'HSI',
    'HSE',
    'PLL',
    'LSI',
    'LSE',
    'SYSCLK',
    'HCLK',
    'PCLK1',
    'PCLK2',
    ...config.availableSources
  ];

  const frequencyUnits = ['Hz', 'KHz', 'MHz'] as const;

  const handleCheckboxChange = (enabled: boolean) => {
    onChange({ clockEnabled: enabled });
  };

  const handleSourceChange = (source: string) => {
    onChange({ clockSource: source });
  };

  const handleFrequencyChange = (frequency: number) => {
    onChange({ frequency });
  };

  const handleUnitChange = (unit: 'Hz' | 'KHz' | 'MHz') => {
    onChange({ unit });
  };

  const isFormValid = () => {
    return config.clockSource && config.frequency > 0;
  };

  return (
    <div className="clock-config-form">
      <h2>Clock Configuration</h2>
      
      <div className="form-group">
        <div className="checkbox-group">
          <input
            type="checkbox"
            id="clock-enabled"
            checked={config.clockEnabled}
            onChange={(e) => handleCheckboxChange(e.target.checked)}
            disabled={disabled}
          />
          <label htmlFor="clock-enabled">
            Enable Clock Configuration
          </label>
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="clock-source">Clock Source</label>
        <select
          id="clock-source"
          value={config.clockSource}
          onChange={(e) => handleSourceChange(e.target.value)}
          disabled={disabled || !config.clockEnabled}
        >
          <option value="">Select clock source...</option>
          {clockSources.map((source) => (
            <option key={source} value={source}>
              {source}
            </option>
          ))}
        </select>
      </div>

      <div className="form-group">
        <label>Clock Frequency</label>
        <div className="frequency-group">
          <div className="frequency-input">
            <input
              type="number"
              value={config.frequency}
              onChange={(e) => handleFrequencyChange(Number(e.target.value))}
              disabled={disabled || !config.clockEnabled}
              min="1"
              step="1"
              placeholder="Enter frequency"
            />
          </div>
          <div className="frequency-unit">
            <select
              value={config.unit}
              onChange={(e) => handleUnitChange(e.target.value as 'Hz' | 'KHz' | 'MHz')}
              disabled={disabled || !config.clockEnabled}
            >
              {frequencyUnits.map((unit) => (
                <option key={unit} value={unit}>
                  {unit}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="button-group">
        <button
          className="btn btn-primary"
          onClick={onSave}
          disabled={disabled || !config.clockEnabled || !isFormValid()}
        >
          Save Configuration
        </button>
        
        <button
          className="btn btn-secondary"
          onClick={() => {
            onChange({
              clockEnabled: false,
              clockSource: '',
              frequency: 0,
              unit: 'MHz'
            });
          }}
          disabled={disabled}
        >
          Reset
        </button>
      </div>

      {config.clockEnabled && (
        <div className="config-summary">
          <h3>Configuration Summary</h3>
          <p><strong>Status:</strong> {config.clockEnabled ? 'Enabled' : 'Disabled'}</p>
          <p><strong>Source:</strong> {config.clockSource || 'Not selected'}</p>
          <p><strong>Frequency:</strong> {config.frequency} {config.unit}</p>
          {config.frequency > 0 && config.unit !== 'Hz' && (
            <p><strong>Frequency (Hz):</strong> {
              config.unit === 'KHz' ? config.frequency * 1000 :
              config.unit === 'MHz' ? config.frequency * 1000000 :
              config.frequency
            } Hz</p>
          )}
        </div>
      )}
    </div>
  );
};
