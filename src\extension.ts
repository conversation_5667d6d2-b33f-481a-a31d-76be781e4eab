import * as vscode from 'vscode';
import { ZephyrProjectProvider } from './treeDataProvider';
import { ClockConfigWebviewProvider } from './webviewProvider';
import { PythonServerManager } from './pythonServer';
import { ZephyrProjectScanner } from './utils/projectScanner';

let pythonServer: PythonServerManager;
let projectProvider: ZephyrProjectProvider;
let webviewProvider: ClockConfigWebviewProvider;

export async function activate(context: vscode.ExtensionContext) {
    console.log('Zephyr Clock Configurator extension is now active!');

    // Initialize Python server
    pythonServer = new PythonServerManager(context);
    await pythonServer.start();

    // Initialize project scanner
    const projectScanner = new ZephyrProjectScanner();

    // Initialize tree data provider
    projectProvider = new ZephyrProjectProvider(projectScanner);
    vscode.window.registerTreeDataProvider('zephyr-projects', projectProvider);

    // Initialize webview provider
    webviewProvider = new ClockConfigWebviewProvider(context.extensionUri, pythonServer);

    // Register commands
    const refreshCommand = vscode.commands.registerCommand('zephyr.refreshProjects', () => {
        projectProvider.refresh();
    });

    const launchConfiguratorCommand = vscode.commands.registerCommand('zephyr.launchConfigurator', 
        async (project?: any) => {
            await webviewProvider.createOrShow(project);
        }
    );

    const selectProjectCommand = vscode.commands.registerCommand('zephyr.selectProject', 
        async (project: any) => {
            await webviewProvider.selectProject(project);
        }
    );

    // Register file system watcher for Zephyr project files
    const watcher = vscode.workspace.createFileSystemWatcher('**/{CMakeLists.txt,prj.conf,*.dtsi,*.yaml}');
    watcher.onDidCreate(() => projectProvider.refresh());
    watcher.onDidDelete(() => projectProvider.refresh());
    watcher.onDidChange(() => projectProvider.refresh());

    context.subscriptions.push(
        refreshCommand,
        launchConfiguratorCommand,
        selectProjectCommand,
        watcher
    );

    // Auto-refresh projects on workspace change
    vscode.workspace.onDidChangeWorkspaceFolders(() => {
        projectProvider.refresh();
    });

    // Initial project scan
    await projectProvider.refresh();
}

export async function deactivate() {
    console.log('Deactivating Zephyr Clock Configurator extension...');

    try {
        // Stop the Python server gracefully
        if (pythonServer) {
            await pythonServer.stop();
        }

        // Close any open webview panels
        if (webviewProvider) {
            webviewProvider.dispose();
        }

        console.log('Extension deactivated successfully');
    } catch (error) {
        console.error('Error during extension deactivation:', error);
    }
}
