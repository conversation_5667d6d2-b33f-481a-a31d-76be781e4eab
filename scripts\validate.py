#!/usr/bin/env python3
"""
Validation script for Zephyr Clock Configurator Extension
This script validates the complete setup and functionality
"""

import os
import sys
import subprocess
import json
import requests
import time
from pathlib import Path


def run_command(command, cwd=None, capture_output=True):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=capture_output,
            text=True,
            timeout=30
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)


def check_prerequisites():
    """Check if all prerequisites are installed"""
    print("🔍 Checking prerequisites...")
    
    checks = [
        ("Node.js", "node --version"),
        ("npm", "npm --version"),
        ("Python", "python --version"),
        ("pip", "pip --version")
    ]
    
    all_good = True
    for name, command in checks:
        success, stdout, stderr = run_command(command)
        if success:
            version = stdout.strip()
            print(f"  ✅ {name}: {version}")
        else:
            print(f"  ❌ {name}: Not found or error")
            all_good = False
    
    return all_good


def validate_project_structure():
    """Validate the project structure"""
    print("📁 Validating project structure...")
    
    required_files = [
        "package.json",
        "tsconfig.json",
        "src/extension.ts",
        "webview-ui/package.json",
        "webview-ui/src/App.tsx",
        "python-backend/main.py",
        "python-backend/requirements.txt",
        "README.md"
    ]
    
    all_good = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}: Missing")
            all_good = False
    
    return all_good


def validate_dependencies():
    """Validate that dependencies are installed"""
    print("📦 Validating dependencies...")
    
    # Check Node.js dependencies
    print("  Checking Node.js dependencies...")
    success, _, _ = run_command("npm list --depth=0")
    if success:
        print("    ✅ VSCode extension dependencies")
    else:
        print("    ❌ VSCode extension dependencies missing")
        return False
    
    # Check React dependencies
    print("  Checking React dependencies...")
    success, _, _ = run_command("npm list --depth=0", cwd="webview-ui")
    if success:
        print("    ✅ React UI dependencies")
    else:
        print("    ❌ React UI dependencies missing")
        return False
    
    # Check Python dependencies
    print("  Checking Python dependencies...")
    success, _, _ = run_command("pip check", cwd="python-backend")
    if success:
        print("    ✅ Python backend dependencies")
    else:
        print("    ❌ Python backend dependencies missing")
        return False
    
    return True


def validate_build():
    """Validate that the project builds correctly"""
    print("🔨 Validating build process...")
    
    # Build TypeScript
    print("  Building TypeScript...")
    success, _, stderr = run_command("npm run compile")
    if success:
        print("    ✅ TypeScript compilation")
    else:
        print(f"    ❌ TypeScript compilation failed: {stderr}")
        return False
    
    # Build React UI
    print("  Building React UI...")
    success, _, stderr = run_command("npm run build", cwd="webview-ui")
    if success:
        print("    ✅ React UI build")
    else:
        print(f"    ❌ React UI build failed: {stderr}")
        return False
    
    return True


def validate_python_backend():
    """Validate Python backend functionality"""
    print("🐍 Validating Python backend...")
    
    # Start the backend server
    print("  Starting Python backend...")
    backend_process = subprocess.Popen(
        ["python", "main.py"],
        cwd="python-backend",
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    # Wait for server to start
    time.sleep(5)
    
    try:
        # Test health endpoint
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("    ✅ Backend server health check")
        else:
            print(f"    ❌ Backend server health check failed: {response.status_code}")
            return False
        
        # Test API endpoints
        response = requests.get("http://127.0.0.1:8000/api/projects", timeout=5)
        if response.status_code == 200:
            print("    ✅ Projects API endpoint")
        else:
            print(f"    ❌ Projects API endpoint failed: {response.status_code}")
            return False
        
    except requests.exceptions.RequestException as e:
        print(f"    ❌ Backend server connection failed: {e}")
        return False
    
    finally:
        # Stop the backend server
        backend_process.terminate()
        backend_process.wait()
    
    return True


def validate_extension_manifest():
    """Validate VSCode extension manifest"""
    print("📋 Validating extension manifest...")
    
    try:
        with open("package.json", "r") as f:
            manifest = json.load(f)
        
        required_fields = [
            "name", "displayName", "description", "version",
            "engines", "categories", "activationEvents",
            "main", "contributes"
        ]
        
        for field in required_fields:
            if field in manifest:
                print(f"    ✅ {field}")
            else:
                print(f"    ❌ {field}: Missing")
                return False
        
        # Check contributes section
        contributes = manifest.get("contributes", {})
        if "viewsContainers" in contributes and "views" in contributes and "commands" in contributes:
            print("    ✅ Extension contributions")
        else:
            print("    ❌ Extension contributions incomplete")
            return False
        
    except Exception as e:
        print(f"    ❌ Error reading package.json: {e}")
        return False
    
    return True


def run_tests():
    """Run available tests"""
    print("🧪 Running tests...")
    
    # Run TypeScript tests
    print("  Running TypeScript tests...")
    success, stdout, stderr = run_command("npm test")
    if success:
        print("    ✅ TypeScript tests passed")
    else:
        print(f"    ⚠️  TypeScript tests: {stderr}")
    
    # Run Python tests
    print("  Running Python tests...")
    success, stdout, stderr = run_command("python -m pytest tests/", cwd="python-backend")
    if success:
        print("    ✅ Python tests passed")
    else:
        print(f"    ⚠️  Python tests: {stderr}")
    
    return True


def main():
    """Main validation function"""
    print("🚀 Zephyr Clock Configurator Extension Validation")
    print("=" * 50)
    
    validation_steps = [
        ("Prerequisites", check_prerequisites),
        ("Project Structure", validate_project_structure),
        ("Dependencies", validate_dependencies),
        ("Extension Manifest", validate_extension_manifest),
        ("Build Process", validate_build),
        ("Python Backend", validate_python_backend),
        ("Tests", run_tests)
    ]
    
    all_passed = True
    for step_name, step_func in validation_steps:
        print(f"\n{step_name}:")
        try:
            if not step_func():
                all_passed = False
        except Exception as e:
            print(f"  ❌ Error during {step_name}: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All validations passed! Extension is ready for use.")
        print("\n📝 Next steps:")
        print("1. Open VSCode: code .")
        print("2. Press F5 to launch Extension Development Host")
        print("3. Open a Zephyr project and test the extension")
    else:
        print("❌ Some validations failed. Please fix the issues and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()
