"""
Clock configuration Pydantic schemas
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator


class ClockConfigBase(BaseModel):
    config_name: str = Field(..., description="Configuration name")
    enabled: bool = Field(False, description="Whether the clock is enabled")
    source: str = Field(..., description="Clock source")
    frequency: int = Field(..., gt=0, description="Clock frequency")
    unit: str = Field("Hz", description="Frequency unit (Hz, KHz, MHz)")
    description: Optional[str] = Field(None, description="Configuration description")

    @validator('unit')
    def validate_unit(cls, v):
        if v not in ['Hz', 'KHz', 'MHz']:
            raise ValueError('Unit must be Hz, KHz, or MHz')
        return v


class ClockConfigCreate(ClockConfigBase):
    project_id: int = Field(..., description="Project ID")
    device_tree_path: Optional[str] = None
    kconfig_option: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = {}


class ClockConfigUpdate(BaseModel):
    config_name: Optional[str] = None
    enabled: Optional[bool] = None
    source: Optional[str] = None
    frequency: Optional[int] = Field(None, gt=0)
    unit: Optional[str] = None
    description: Optional[str] = None
    device_tree_path: Optional[str] = None
    kconfig_option: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    @validator('unit')
    def validate_unit(cls, v):
        if v is not None and v not in ['Hz', 'KHz', 'MHz']:
            raise ValueError('Unit must be Hz, KHz, or MHz')
        return v


class ClockConfigResponse(ClockConfigBase):
    id: int
    project_id: int
    device_tree_path: Optional[str]
    kconfig_option: Optional[str]
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ClockSourceInfo(BaseModel):
    name: str
    description: Optional[str] = None
    default_frequency: Optional[int] = None
    supported_frequencies: Optional[List[int]] = None
    device_tree_path: Optional[str] = None


class ClockConfigSummary(BaseModel):
    total_configs: int
    enabled_configs: int
    available_sources: List[ClockSourceInfo]
    frequency_range: Dict[str, int]  # min, max frequencies
