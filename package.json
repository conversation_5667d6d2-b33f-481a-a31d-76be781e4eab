{"name": "zephyr-clock-configurator", "displayName": "Zephyr Clock Configurator", "description": "A comprehensive clock configuration tool for Zephyr RTOS projects", "version": "0.1.0", "publisher": "zephyr-tools", "repository": {"type": "git", "url": "https://github.com/zephyr-tools/clock-configurator"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["workspaceContains:**/CMakeLists.txt", "workspaceContains:**/prj.conf"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "zephyr-clock-config", "title": "Zephyr Clock Config", "icon": "$(clock)"}]}, "views": {"zephyr-clock-config": [{"id": "zephyr-projects", "name": "Zephyr Projects", "when": "workbenchState != empty"}]}, "commands": [{"command": "zephyr.refreshProjects", "title": "Refresh Projects", "icon": "$(refresh)"}, {"command": "zephyr.launchConfigurator", "title": "Launch Configurator", "icon": "$(gear)"}, {"command": "zephyr.selectProject", "title": "Select Project"}], "menus": {"view/title": [{"command": "zephyr.refreshProjects", "when": "view == zephyr-projects", "group": "navigation"}], "view/item/context": [{"command": "zephyr.launchConfigurator", "when": "view == zephyr-projects && viewItem == zephyrProject", "group": "inline"}]}}, "scripts": {"vscode:prepublish": "npm run build-all", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "build-webview": "cd webview-ui && npm run build", "dev-webview": "cd webview-ui && npm run dev", "setup-python": "cd python-backend && pip install -r requirements.txt", "build-all": "npm run build-webview && npm run compile", "package": "npm run build-all && vsce package", "install-extension": "npm run package && code --install-extension *.vsix"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "eslint": "^8.28.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}}