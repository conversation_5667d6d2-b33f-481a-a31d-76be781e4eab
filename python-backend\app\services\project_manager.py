"""
Project management service
"""

import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload

from app.core.database import AsyncSessionLocal
from app.models.project import Project
from app.models.clock_config import ClockConfig
from app.schemas.project import ProjectCreate, ProjectUpdate, ProjectScanRequest
from app.services.file_parser import ZephyrFileParser, ClockConfigData

logger = logging.getLogger(__name__)


class ProjectManager:
    """Service for managing Zephyr projects"""

    def __init__(self):
        self.file_parser = ZephyrFileParser()

    async def get_projects(self) -> List[Project]:
        """Get all projects"""
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                select(Project).options(selectinload(Project.clock_configs))
            )
            return result.scalars().all()

    async def get_project(self, project_id: int) -> Optional[Project]:
        """Get project by ID"""
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                select(Project)
                .options(selectinload(Project.clock_configs))
                .where(Project.id == project_id)
            )
            return result.scalar_one_or_none()

    async def get_project_by_path(self, project_path: str) -> Optional[Project]:
        """Get project by path"""
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                select(Project)
                .options(selectinload(Project.clock_configs))
                .where(Project.path == project_path)
            )
            return result.scalar_one_or_none()

    async def create_project(self, project_data: ProjectCreate) -> Project:
        """Create a new project"""
        async with AsyncSessionLocal() as session:
            project = Project(
                name=project_data.name,
                path=project_data.path,
                board_config=project_data.board_config,
                cmake_file=project_data.cmake_file,
                prj_conf_file=project_data.prj_conf_file,
                dtsi_files=project_data.dtsi_files,
                yaml_files=project_data.yaml_files,
                kconfig_files=project_data.kconfig_files,
                metadata=project_data.metadata
            )
            
            session.add(project)
            await session.commit()
            await session.refresh(project)
            return project

    async def update_project(self, project_id: int, project_data: ProjectUpdate) -> Optional[Project]:
        """Update an existing project"""
        async with AsyncSessionLocal() as session:
            project = await session.get(Project, project_id)
            if not project:
                return None

            update_data = project_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(project, field, value)

            project.updated_at = datetime.utcnow()
            await session.commit()
            await session.refresh(project)
            return project

    async def delete_project(self, project_id: int) -> bool:
        """Delete a project"""
        async with AsyncSessionLocal() as session:
            project = await session.get(Project, project_id)
            if not project:
                return False

            await session.delete(project)
            await session.commit()
            return True

    async def scan_project(self, scan_request: ProjectScanRequest) -> Dict[str, Any]:
        """Scan a project for clock configurations"""
        project_path = Path(scan_request.project_path)
        
        if not project_path.exists():
            raise ValueError(f"Project path does not exist: {project_path}")

        # Check if project already exists
        existing_project = await self.get_project_by_path(str(project_path))
        
        # Check if we need to rescan
        if existing_project and not scan_request.force_rescan:
            # Skip if scanned recently (within last hour)
            if (existing_project.last_scanned and 
                existing_project.last_scanned > datetime.utcnow() - timedelta(hours=1)):
                return {
                    "project": existing_project,
                    "clock_configs_found": len(existing_project.clock_configs),
                    "errors": [],
                    "warnings": ["Project was recently scanned, use force_rescan=true to rescan"]
                }

        try:
            # Parse project files
            parse_result = self.file_parser.parse_project(str(project_path))
            
            # Create or update project
            if existing_project:
                project = await self._update_project_from_scan(existing_project, project_path, parse_result)
            else:
                project = await self._create_project_from_scan(project_path, parse_result)

            # Update clock configurations
            await self._update_clock_configs(project, parse_result.clock_configs)

            return {
                "project": project,
                "clock_configs_found": len(parse_result.clock_configs),
                "errors": parse_result.errors,
                "warnings": parse_result.warnings
            }

        except Exception as e:
            logger.error(f"Error scanning project {project_path}: {e}")
            raise

    async def _create_project_from_scan(self, project_path: Path, parse_result) -> Project:
        """Create a new project from scan results"""
        # Collect file paths
        dtsi_files = []
        yaml_files = []
        kconfig_files = []

        for file_path in parse_result.device_tree_data.keys():
            if file_path.endswith(('.dtsi', '.overlay')):
                dtsi_files.append(file_path)
            elif file_path.endswith(('.yaml', '.yml')):
                yaml_files.append(file_path)

        for file_path in parse_result.kconfig_data.keys():
            kconfig_files.append(file_path)

        # Find CMakeLists.txt and prj.conf
        cmake_file = None
        prj_conf_file = None
        
        cmake_path = project_path / "CMakeLists.txt"
        if cmake_path.exists():
            cmake_file = str(cmake_path)

        prj_conf_path = project_path / "prj.conf"
        if prj_conf_path.exists():
            prj_conf_file = str(prj_conf_path)

        # Determine board config (simplified)
        board_config = "unknown"
        # You could implement more sophisticated board detection here

        project_data = ProjectCreate(
            name=project_path.name,
            path=str(project_path),
            board_config=board_config,
            cmake_file=cmake_file,
            prj_conf_file=prj_conf_file,
            dtsi_files=dtsi_files,
            yaml_files=yaml_files,
            kconfig_files=kconfig_files,
            metadata={
                "scan_timestamp": datetime.utcnow().isoformat(),
                "device_tree_data": parse_result.device_tree_data,
                "kconfig_data": parse_result.kconfig_data
            }
        )

        return await self.create_project(project_data)

    async def _update_project_from_scan(self, project: Project, project_path: Path, parse_result) -> Project:
        """Update existing project from scan results"""
        # Update file lists
        dtsi_files = []
        yaml_files = []
        kconfig_files = []

        for file_path in parse_result.device_tree_data.keys():
            if file_path.endswith(('.dtsi', '.overlay')):
                dtsi_files.append(file_path)
            elif file_path.endswith(('.yaml', '.yml')):
                yaml_files.append(file_path)

        for file_path in parse_result.kconfig_data.keys():
            kconfig_files.append(file_path)

        update_data = ProjectUpdate(
            dtsi_files=dtsi_files,
            yaml_files=yaml_files,
            kconfig_files=kconfig_files,
            metadata={
                "scan_timestamp": datetime.utcnow().isoformat(),
                "device_tree_data": parse_result.device_tree_data,
                "kconfig_data": parse_result.kconfig_data
            }
        )

        # Update last_scanned timestamp
        async with AsyncSessionLocal() as session:
            await session.execute(
                update(Project)
                .where(Project.id == project.id)
                .values(last_scanned=datetime.utcnow())
            )
            await session.commit()

        return await self.update_project(project.id, update_data)

    async def _update_clock_configs(self, project: Project, clock_configs: List[ClockConfigData]):
        """Update clock configurations for a project"""
        async with AsyncSessionLocal() as session:
            # Delete existing clock configs for this project
            await session.execute(
                delete(ClockConfig).where(ClockConfig.project_id == project.id)
            )

            # Add new clock configs
            for config_data in clock_configs:
                clock_config = ClockConfig(
                    project_id=project.id,
                    config_name=config_data.name,
                    enabled=config_data.enabled,
                    source=config_data.source,
                    frequency=config_data.frequency,
                    unit=config_data.unit,
                    description=config_data.description,
                    device_tree_path=config_data.device_tree_path,
                    kconfig_option=config_data.kconfig_option,
                    metadata=config_data.metadata or {}
                )
                session.add(clock_config)

            await session.commit()

    async def get_clock_configs(self, project_id: int) -> List[ClockConfig]:
        """Get clock configurations for a project"""
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                select(ClockConfig).where(ClockConfig.project_id == project_id)
            )
            return result.scalars().all()

    async def update_clock_config(self, config_id: int, config_data: Dict[str, Any]) -> Optional[ClockConfig]:
        """Update a clock configuration"""
        async with AsyncSessionLocal() as session:
            config = await session.get(ClockConfig, config_id)
            if not config:
                return None

            for field, value in config_data.items():
                if hasattr(config, field):
                    setattr(config, field, value)

            config.updated_at = datetime.utcnow()
            await session.commit()
            await session.refresh(config)
            return config
