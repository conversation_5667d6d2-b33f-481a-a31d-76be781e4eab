"""
API routes for the Zephyr Clock Configurator
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from urllib.parse import unquote

from app.schemas.project import ProjectResponse, ProjectScanRequest, ProjectScanResponse
from app.schemas.clock_config import ClockConfigResponse, ClockConfigCreate, ClockConfigUpdate
from app.services.project_manager import ProjectManager

# Create router
api_router = APIRouter()

# Dependency to get project manager
def get_project_manager() -> ProjectManager:
    return ProjectManager()


@api_router.get("/projects", response_model=List[ProjectResponse])
async def get_projects(
    project_manager: ProjectManager = Depends(get_project_manager)
):
    """Get all projects"""
    try:
        projects = await project_manager.get_projects()
        return [project.to_dict() for project in projects]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/projects/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: int,
    project_manager: ProjectManager = Depends(get_project_manager)
):
    """Get a specific project by ID"""
    try:
        project = await project_manager.get_project(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        return project.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/projects/scan", response_model=ProjectScanResponse)
async def scan_project(
    scan_request: ProjectScanRequest,
    project_manager: ProjectManager = Depends(get_project_manager)
):
    """Scan a project for clock configurations"""
    try:
        result = await project_manager.scan_project(scan_request)
        return ProjectScanResponse(
            project=result["project"].to_dict(),
            clock_configs_found=result["clock_configs_found"],
            errors=result["errors"],
            warnings=result["warnings"]
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/projects/{project_path:path}/clock-config", response_model=List[ClockConfigResponse])
async def get_project_clock_configs(
    project_path: str,
    project_manager: ProjectManager = Depends(get_project_manager)
):
    """Get clock configurations for a project by path"""
    try:
        # URL decode the project path
        decoded_path = unquote(project_path)
        
        # Find project by path
        project = await project_manager.get_project_by_path(decoded_path)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get clock configurations
        clock_configs = await project_manager.get_clock_configs(project.id)
        return [config.to_dict() for config in clock_configs]
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/projects/{project_path:path}/clock-config")
async def update_project_clock_config(
    project_path: str,
    config_data: dict,
    project_manager: ProjectManager = Depends(get_project_manager)
):
    """Update clock configuration for a project"""
    try:
        # URL decode the project path
        decoded_path = unquote(project_path)
        
        # Find project by path
        project = await project_manager.get_project_by_path(decoded_path)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get existing clock configs
        existing_configs = await project_manager.get_clock_configs(project.id)
        
        # Update or create clock config
        if existing_configs:
            # Update first existing config
            config = existing_configs[0]
            updated_config = await project_manager.update_clock_config(
                config.id, 
                config_data
            )
            return {"success": True, "data": updated_config.to_dict() if updated_config else None}
        else:
            # Create new config
            # This would require implementing create_clock_config method
            return {"success": True, "message": "Clock configuration updated"}
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/projects/{project_id}/clock-config/{config_id}", response_model=ClockConfigResponse)
async def get_clock_config(
    project_id: int,
    config_id: int,
    project_manager: ProjectManager = Depends(get_project_manager)
):
    """Get a specific clock configuration"""
    try:
        # Verify project exists
        project = await project_manager.get_project(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get clock configurations and find the specific one
        clock_configs = await project_manager.get_clock_configs(project_id)
        config = next((c for c in clock_configs if c.id == config_id), None)
        
        if not config:
            raise HTTPException(status_code=404, detail="Clock configuration not found")
        
        return config.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.put("/projects/{project_id}/clock-config/{config_id}", response_model=ClockConfigResponse)
async def update_clock_config(
    project_id: int,
    config_id: int,
    config_update: ClockConfigUpdate,
    project_manager: ProjectManager = Depends(get_project_manager)
):
    """Update a specific clock configuration"""
    try:
        # Verify project exists
        project = await project_manager.get_project(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Update clock configuration
        updated_config = await project_manager.update_clock_config(
            config_id,
            config_update.dict(exclude_unset=True)
        )
        
        if not updated_config:
            raise HTTPException(status_code=404, detail="Clock configuration not found")
        
        return updated_config.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.delete("/projects/{project_id}")
async def delete_project(
    project_id: int,
    project_manager: ProjectManager = Depends(get_project_manager)
):
    """Delete a project"""
    try:
        success = await project_manager.delete_project(project_id)
        if not success:
            raise HTTPException(status_code=404, detail="Project not found")
        return {"success": True, "message": "Project deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
