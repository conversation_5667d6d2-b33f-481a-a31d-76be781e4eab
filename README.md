# Zephyr Clock Configurator VSCode Extension

A comprehensive VSCode extension for configuring clock settings in Zephyr RTOS projects. This extension provides a graphical interface for managing clock configurations with real-time project scanning and file monitoring.

## Features

- **Project Detection**: Automatically scans workspace for Zephyr projects
- **Clock Configuration**: Intuitive UI for setting clock sources and frequencies
- **Real-time Monitoring**: Watches project files for changes and updates configurations
- **Multi-format Support**: Parses device tree (.dtsi), YAML, and Kconfig files
- **Backend Integration**: Python FastAPI backend for robust file parsing and data management

## Architecture

The extension consists of three main components:

1. **VSCode Extension** (TypeScript): Provides the main extension interface and project tree view
2. **React Webview UI** (TypeScript): Interactive clock configuration interface
3. **Python Backend** (FastAPI): File parsing, database management, and API services

## Installation

### Quick Installation (Recommended)

1. **Download the pre-built extension**:
   - Download `zephyr-clock-configurator-0.1.0.vsix` from the releases

2. **Install the extension**:
   ```bash
   code --install-extension zephyr-clock-configurator-0.1.0.vsix
   ```

3. **Restart VSCode** to activate the extension

### Prerequisites

- VSCode 1.74.0 or higher
- Python 3.9+ (automatically managed by the extension)
- Zephyr SDK (for working with real projects)

### Development Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd zephyr-clock-configurator
   ```

2. **Install dependencies and build**:
   ```bash
   npm install
   npm run build-all
   ```

3. **Create extension package**:
   ```bash
   npm run package
   ```

4. **Install the extension**:
   ```bash
   npm run install-extension
   ```

4. **Install Python backend dependencies**:
   ```bash
   cd python-backend
   pip install -r requirements.txt
   cd ..
   ```

5. **Build the extension**:
   ```bash
   npm run compile
   npm run build-webview
   ```

6. **Install the extension**:
   - Open VSCode
   - Press `F5` to launch Extension Development Host
   - Or package with `vsce package` and install the .vsix file

## Usage

### Getting Started

1. **Open a Zephyr project workspace** in VSCode
2. **Look for the Zephyr Clock Config icon** 🕐 in the Activity Bar (left sidebar)
3. **The Python backend server starts automatically** when you first use the extension
4. **Browse detected projects** in the tree view
5. **Click "Launch Configurator"** on any project to open the configuration UI
6. **Configure clock settings** using the intuitive interface
7. **Save configurations** - changes are automatically applied to project files

### Key Features

#### 🚀 Automatic Server Management
- **Auto-start**: Python backend server starts automatically when extension loads
- **Auto-stop**: Server stops gracefully when VSCode closes or extension deactivates
- **Health monitoring**: Real-time server status monitoring
- **Error handling**: Automatic recovery and user notifications

#### 📁 Smart Project Detection
- Automatically scans workspace for Zephyr projects
- Detects CMakeLists.txt, prj.conf, and device tree files
- Real-time monitoring of file changes
- Hierarchical project view with status indicators

#### ⚙️ Intuitive Clock Configuration
- Visual interface for clock source selection
- Frequency input with unit conversion (Hz, KHz, MHz)
- Real-time validation and error checking
- Configuration preview and summary
- Live updates from backend data

#### 🔄 Seamless File Integration
- Parses existing device tree configurations
- Updates .dtsi and overlay files automatically
- Integrates with Kconfig options
- Maintains file formatting and comments
- Backend API for robust data management

## Development

### Project Structure

```
zephyr-clock-configurator/
├── src/                          # VSCode extension source
│   ├── extension.ts              # Main extension entry point
│   ├── treeDataProvider.ts       # Project tree view
│   ├── webviewProvider.ts        # React webview integration
│   ├── pythonServer.ts           # Python backend communication
│   └── utils/                    # Utility modules
├── webview-ui/                   # React TypeScript UI
│   ├── src/
│   │   ├── App.tsx               # Main React component
│   │   ├── components/           # UI components
│   │   └── types.ts              # TypeScript definitions
├── python-backend/               # Python FastAPI backend
│   ├── app/
│   │   ├── api/                  # API routes
│   │   ├── core/                 # Core configuration
│   │   ├── models/               # Database models
│   │   ├── schemas/              # Pydantic schemas
│   │   └── services/             # Business logic
│   └── main.py                   # FastAPI application
└── package.json                  # Extension manifest
```

### Building and Testing

1. **Compile TypeScript**:
   ```bash
   npm run compile
   ```

2. **Build React UI**:
   ```bash
   npm run build-webview
   ```

3. **Start Python backend** (for development):
   ```bash
   cd python-backend
   python main.py
   ```

4. **Run tests**:
   ```bash
   npm test
   ```

5. **Launch Extension Development Host**:
   ```bash
   code --extensionDevelopmentPath=.
   ```

### API Endpoints

The Python backend provides the following REST API endpoints:

- `GET /api/projects` - List all projects
- `POST /api/projects/scan` - Scan a project for configurations
- `GET /api/projects/{project_path}/clock-config` - Get clock configurations
- `POST /api/projects/{project_path}/clock-config` - Update clock configuration

## Configuration

### Extension Settings

The extension can be configured through VSCode settings:

- `zephyr.clockConfig.autoScan`: Enable automatic project scanning
- `zephyr.clockConfig.pythonPath`: Custom Python executable path
- `zephyr.clockConfig.serverPort`: Backend server port (default: 8000)

### File Patterns

The extension monitors the following file types:
- Device tree files: `*.dtsi`, `*.overlay`
- Configuration files: `prj.conf`, `*.conf`
- YAML files: `*.yaml`, `*.yml`
- Build files: `CMakeLists.txt`
- Kconfig files: `Kconfig*`

## Troubleshooting

### Common Issues

1. **Python backend fails to start**:
   - Ensure Python 3.9+ is installed and in PATH
   - Check that all dependencies are installed: `pip install -r requirements.txt`
   - Verify port 8000 is not in use

2. **Projects not detected**:
   - Ensure the workspace contains valid Zephyr projects
   - Check for `CMakeLists.txt` or `prj.conf` files
   - Use "Refresh Projects" command

3. **Clock configuration not saving**:
   - Verify backend server is running
   - Check file permissions in project directory
   - Review error messages in the extension output

### Debug Mode

Enable debug logging by setting the environment variable:
```bash
export ZEPHYR_DEBUG=1
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Style

- TypeScript: Follow ESLint configuration
- Python: Follow PEP 8 style guide
- React: Use functional components with hooks

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the extension output logs
