{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": "build", "presentation": {"panel": "shared"}, "problemMatcher": "$tsc"}, {"type": "npm", "script": "watch", "group": "build", "presentation": {"panel": "shared"}, "isBackground": true, "problemMatcher": "$tsc-watch"}, {"type": "npm", "script": "build-webview", "group": "build", "presentation": {"panel": "shared"}, "problemMatcher": []}, {"label": "Setup Python Backend", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "options": {"cwd": "${workspaceFolder}/python-backend"}, "group": "build", "presentation": {"panel": "shared"}}, {"label": "Start Python Backend", "type": "shell", "command": "python", "args": ["main.py"], "options": {"cwd": "${workspaceFolder}/python-backend", "env": {"ZEPHYR_BACKEND_HOST": "127.0.0.1", "ZEPHYR_BACKEND_PORT": "8000", "PYTHONPATH": "${workspaceFolder}/python-backend"}}, "group": "build", "presentation": {"panel": "shared"}, "isBackground": true}, {"label": "Build All", "dependsOrder": "sequence", "dependsOn": ["npm: compile", "npm: build-webview"], "group": "build"}]}