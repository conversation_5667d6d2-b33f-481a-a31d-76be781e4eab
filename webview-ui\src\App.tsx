import { useState, useEffect, useCallback } from 'react';
import { ClockConfigState, WebviewMessage, VSCodeAPI } from './types';
import { ClockConfigurator } from './components/ClockConfigurator';
import { ProjectInfo } from './components/ProjectInfo';
import { StatusIndicator } from './components/StatusIndicator';
import { ErrorMessage } from './components/ErrorMessage';
import { LoadingSpinner } from './components/LoadingSpinner';
import axios from 'axios';

// Get VSCode API
const vscode: VSCodeAPI = window.acquireVsCodeApi();

// Backend API base URL
const API_BASE_URL = 'http://127.0.0.1:8000/api';

function App() {
  const [state, setState] = useState<ClockConfigState>({
    selectedProject: null,
    clockEnabled: false,
    clockSource: '',
    frequency: 0,
    unit: 'MHz',
    isLoading: false,
    error: null,
    availableSources: [],
    serverStatus: {
      isRunning: false,
      serverUrl: ''
    }
  });

  // Check server status
  const checkServerStatus = useCallback(async () => {
    try {
      await axios.get(`${API_BASE_URL}/../health`, { timeout: 2000 });
      setState(prev => ({
        ...prev,
        serverStatus: {
          isRunning: true,
          serverUrl: API_BASE_URL
        },
        error: null
      }));
      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        serverStatus: {
          isRunning: false,
          serverUrl: ''
        },
        error: 'Backend server is not running'
      }));
      return false;
    }
  }, []);

  // Fetch clock configuration from backend
  const fetchClockConfig = useCallback(async (projectPath: string) => {
    if (!state.serverStatus.isRunning) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const encodedPath = encodeURIComponent(projectPath);
      const response = await axios.get(`${API_BASE_URL}/projects/${encodedPath}/clock-config`);

      if (response.data && response.data.length > 0) {
        const config = response.data[0];
        setState(prev => ({
          ...prev,
          clockEnabled: config.enabled,
          clockSource: config.source,
          frequency: config.frequency,
          unit: config.unit,
          isLoading: false
        }));
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `Failed to fetch clock configuration: ${error}`,
        isLoading: false
      }));
    }
  }, [state.serverStatus.isRunning]);

  // Save clock configuration to backend
  const saveClockConfig = useCallback(async () => {
    if (!state.selectedProject || !state.serverStatus.isRunning) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const encodedPath = encodeURIComponent(state.selectedProject.path);
      const configData = {
        enabled: state.clockEnabled,
        source: state.clockSource,
        frequency: state.frequency,
        unit: state.unit
      };

      await axios.post(`${API_BASE_URL}/projects/${encodedPath}/clock-config`, configData);

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: null
      }));

      // Notify VSCode extension of successful save
      vscode.postMessage({
        type: 'CLOCK_CONFIG_SAVED',
        payload: { success: true, config: configData }
      });

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `Failed to save clock configuration: ${error}`,
        isLoading: false
      }));
    }
  }, [state.selectedProject, state.serverStatus.isRunning, state.clockEnabled, state.clockSource, state.frequency, state.unit]);

  // Handle messages from VSCode extension
  const handleMessage = useCallback((event: MessageEvent) => {
    const message: WebviewMessage = event.data;

    switch (message.type) {
      case 'PROJECT_SELECTED':
        const project = message.payload.project;
        setState(prev => ({
          ...prev,
          selectedProject: project,
          serverStatus: {
            ...prev.serverStatus,
            serverUrl: message.payload.serverUrl
          }
        }));

        // Fetch clock config for the selected project
        if (project && project.path) {
          fetchClockConfig(project.path);
        }
        break;

      case 'CLOCK_CONFIG_DATA':
        if (message.payload.success) {
          const configs = message.payload.data || [];
          if (configs.length > 0) {
            const config = configs[0]; // Use first config for now
            setState(prev => ({
              ...prev,
              clockEnabled: config.enabled,
              clockSource: config.source,
              frequency: config.frequency,
              unit: config.unit,
              isLoading: false,
              error: null
            }));
          }
        } else {
          setState(prev => ({
            ...prev,
            error: message.payload.error,
            isLoading: false
          }));
        }
        break;

      case 'CLOCK_CONFIG_UPDATED':
        if (message.payload.success) {
          setState(prev => ({
            ...prev,
            error: null,
            isLoading: false
          }));
        } else {
          setState(prev => ({
            ...prev,
            error: message.payload.error,
            isLoading: false
          }));
        }
        break;

      case 'SERVER_STATUS':
        setState(prev => ({
          ...prev,
          serverStatus: message.payload
        }));
        break;

      case 'ERROR':
        setState(prev => ({
          ...prev,
          error: message.payload.error,
          isLoading: false
        }));
        break;

      default:
        console.warn('Unknown message type:', message.type);
    }
  }, [fetchClockConfig]);

  // Set up message listener
  useEffect(() => {
    window.addEventListener('message', handleMessage);

    // Check server status on startup
    checkServerStatus();

    // Notify extension that webview is ready
    vscode.postMessage({ type: 'WEBVIEW_READY' });

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [handleMessage, checkServerStatus]);

  // Periodic server status check
  useEffect(() => {
    const interval = setInterval(() => {
      checkServerStatus();
    }, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, [checkServerStatus]);

  // Load clock configuration when project is selected
  useEffect(() => {
    if (state.selectedProject && state.serverStatus.isRunning) {
      fetchClockConfig(state.selectedProject.path);
    }
  }, [state.selectedProject, state.serverStatus.isRunning, fetchClockConfig]);

  const handleClockConfigChange = (config: Partial<ClockConfigState>) => {
    setState(prev => ({ ...prev, ...config }));
  };

  const handleSaveConfig = () => {
    if (!state.selectedProject) {
      setState(prev => ({ ...prev, error: 'No project selected' }));
      return;
    }

    if (!state.serverStatus.isRunning) {
      setState(prev => ({ ...prev, error: 'Backend server is not running' }));
      return;
    }

    // Use the backend API to save configuration
    saveClockConfig();
  };

  const handleScanProject = () => {
    if (!state.selectedProject) {
      setState(prev => ({ ...prev, error: 'No project selected' }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true }));
    
    vscode.postMessage({
      type: 'SCAN_PROJECT',
      payload: { projectPath: state.selectedProject.path }
    });
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  return (
    <div className="container">
      <div className="header">
        <h1>Zephyr Clock Configurator</h1>
        {state.isLoading && <LoadingSpinner />}
      </div>

      <StatusIndicator 
        isConnected={state.serverStatus.isRunning}
        serverUrl={state.serverStatus.serverUrl}
      />

      {state.error && (
        <ErrorMessage 
          message={state.error} 
          onDismiss={clearError}
        />
      )}

      {state.selectedProject ? (
        <>
          <ProjectInfo 
            project={state.selectedProject}
            onScanProject={handleScanProject}
            isScanning={state.isLoading}
          />
          
          <ClockConfigurator
            config={state}
            onChange={handleClockConfigChange}
            onSave={handleSaveConfig}
            disabled={!state.serverStatus.isRunning || state.isLoading}
          />
        </>
      ) : (
        <div className="project-info">
          <h2>No Project Selected</h2>
          <p>Please select a Zephyr project from the sidebar to configure clock settings.</p>
        </div>
      )}
    </div>
  );
}

export default App;
