@echo off
REM Zephyr Clock Configurator Extension Setup Script for Windows
REM This script sets up the development environment for the extension

echo 🚀 Setting up Zephyr Clock Configurator Extension...

REM Check prerequisites
echo 📋 Checking prerequisites...

REM Check Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ and try again.
    exit /b 1
)

REM Check Python
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python is not installed. Please install Python 3.9+ and try again.
    exit /b 1
)

echo ✅ Prerequisites check passed

REM Install VSCode extension dependencies
echo 📦 Installing VSCode extension dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to install VSCode extension dependencies
    exit /b 1
)

REM Install React UI dependencies
echo 📦 Installing React UI dependencies...
cd webview-ui
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to install React UI dependencies
    exit /b 1
)
cd ..

REM Install Python backend dependencies
echo 📦 Installing Python backend dependencies...
cd python-backend
python -m pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to install Python backend dependencies
    exit /b 1
)
cd ..

REM Build the extension
echo 🔨 Building the extension...
call npm run compile
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to compile extension
    exit /b 1
)

REM Build React UI
echo 🔨 Building React UI...
call npm run build-webview
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to build React UI
    exit /b 1
)

echo ✅ Setup completed successfully!
echo.
echo 🎯 Next steps:
echo 1. Open VSCode in this directory: code .
echo 2. Press F5 to launch the Extension Development Host
echo 3. Open a Zephyr project in the new VSCode window
echo 4. Look for the clock icon in the Activity Bar
echo.
echo 🐛 For debugging:
echo - Use 'npm run watch' to watch TypeScript changes
echo - Use 'npm run dev-webview' to develop React UI
echo - Use the Python Backend launch configuration in VSCode
echo.
echo 📚 See README.md for detailed usage instructions

pause
